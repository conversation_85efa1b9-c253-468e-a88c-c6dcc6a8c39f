/* CSS Custom Properties for Theme Support */
:root {
    /* Light Theme Colors */
    --primary-color: #2563eb;
    --secondary-color: #1e40af;
    --accent-color: #3b82f6;
    --background-color: #ffffff;
    --surface-color: #f8fafc;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    --border-color: #e2e8f0;
    --shadow-light: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-large: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* Dark Theme */
[data-theme="dark"] {
    --primary-color: #3b82f6;
    --secondary-color: #2563eb;
    --accent-color: #60a5fa;
    --background-color: #0f172a;
    --surface-color: #1e293b;
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --text-muted: #64748b;
    --border-color: #334155;
    --shadow-light: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
    --shadow-medium: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
    --shadow-large: 0 20px 25px -5px rgba(0, 0, 0, 0.3);
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-color);
    transition: background-color var(--transition-normal), color var(--transition-normal);
}

/* Progress Bar */
.progress-bar {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    z-index: 1000;
    transition: width var(--transition-fast);
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

/* Header */
.header {
    position: sticky;
    top: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    z-index: 100;
    transition: all var(--transition-normal);
}

[data-theme="dark"] .header {
    background: rgba(15, 23, 42, 0.95);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) 0;
}

.logo {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.logo i {
    font-size: var(--font-size-2xl);
}

.theme-toggle {
    background: none;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm);
    color: var(--text-primary);
    cursor: pointer;
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
}

.theme-toggle:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: scale(1.05);
}

/* Main Content */
.main {
    padding: var(--spacing-2xl) 0;
}

/* Hero Section */
.hero {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
    margin-bottom: var(--spacing-3xl);
}

.category {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-sm);
    font-weight: 500;
    margin-bottom: var(--spacing-md);
}

.hero-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing-lg);
    background: linear-gradient(135deg, var(--text-primary), var(--primary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-meta {
    display: flex;
    gap: var(--spacing-lg);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.hero-meta span {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.image-placeholder {
    width: 200px;
    height: 200px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 4rem;
    box-shadow: var(--shadow-large);
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Article Styles */
.article {
    max-width: 800px;
    margin: 0 auto;
}

.article-section {
    margin-bottom: var(--spacing-3xl);
}

.lead {
    font-size: var(--font-size-lg);
    font-weight: 400;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.7;
}

.section-title {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-primary);
}

.section-title i {
    color: var(--primary-color);
}

p {
    margin-bottom: var(--spacing-md);
    line-height: 1.7;
    color: var(--text-secondary);
}

/* Concept Grid */
.concept-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
}

.concept-card {
    background: var(--surface-color);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.concept-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
}

.concept-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-large);
}

.concept-card h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

/* Application Examples */
.application-examples {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
}

.example-item {
    background: var(--surface-color);
    padding: var(--spacing-lg);
    border-radius: var(--radius-md);
    border-left: 4px solid var(--primary-color);
}

.example-item h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

/* Protection Strategies */
.protection-strategies {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
}

.strategy-item {
    text-align: center;
    padding: var(--spacing-lg);
    background: var(--surface-color);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
}

.strategy-item:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

.strategy-item i {
    font-size: var(--font-size-3xl);
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.strategy-item h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

/* Conclusion CTA */
.conclusion-cta {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg);
    text-align: center;
    margin: var(--spacing-xl) 0;
}

.conclusion-cta p {
    color: white;
    margin: 0;
    font-size: var(--font-size-lg);
}

/* Footer */
.footer {
    background: var(--surface-color);
    border-top: 1px solid var(--border-color);
    padding: var(--spacing-xl) 0;
    margin-top: var(--spacing-3xl);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.footer-links {
    display: flex;
    gap: var(--spacing-lg);
}

.footer-link {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color var(--transition-normal);
}

.footer-link:hover {
    color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-md);
    }
    
    .hero {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--spacing-lg);
    }
    
    .hero-title {
        font-size: var(--font-size-3xl);
    }
    
    .concept-grid {
        grid-template-columns: 1fr;
    }
    
    .protection-strategies {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        flex-direction: column;
        text-align: center;
    }
    
    .hero-meta {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: var(--font-size-2xl);
    }
    
    .section-title {
        font-size: var(--font-size-xl);
    }
    
    .image-placeholder {
        width: 150px;
        height: 150px;
        font-size: 3rem;
    }
}
