// Modern JavaScript for Interactive Blog Features

class BlogInteractivity {
    constructor() {
        this.init();
    }

    init() {
        this.setupThemeToggle();
        this.setupReadingProgress();
        this.setupSmoothScrolling();
        this.setupAnimations();
        this.setupKeyboardNavigation();
        this.loadUserPreferences();
    }

    // Theme Toggle Functionality
    setupThemeToggle() {
        const themeToggle = document.getElementById('themeToggle');
        const body = document.body;
        
        themeToggle.addEventListener('click', () => {
            const currentTheme = body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            body.setAttribute('data-theme', newTheme);
            this.updateThemeIcon(newTheme);
            this.saveUserPreference('theme', newTheme);
            
            // Add a subtle animation to the toggle
            themeToggle.style.transform = 'scale(0.9)';
            setTimeout(() => {
                themeToggle.style.transform = 'scale(1)';
            }, 150);
        });
    }

    updateThemeIcon(theme) {
        const themeToggle = document.getElementById('themeToggle');
        const icon = themeToggle.querySelector('i');
        
        if (theme === 'dark') {
            icon.className = 'fas fa-sun';
        } else {
            icon.className = 'fas fa-moon';
        }
    }

    // Reading Progress Bar
    setupReadingProgress() {
        const progressBar = document.getElementById('progressBar');
        
        window.addEventListener('scroll', () => {
            const windowHeight = window.innerHeight;
            const documentHeight = document.documentElement.scrollHeight - windowHeight;
            const scrollTop = window.pageYOffset;
            const progress = (scrollTop / documentHeight) * 100;
            
            progressBar.style.width = `${Math.min(progress, 100)}%`;
        });
    }

    // Smooth Scrolling for Internal Links
    setupSmoothScrolling() {
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(anchor.getAttribute('href'));
                
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    // Intersection Observer for Animations
    setupAnimations() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);

        // Observe elements for animation
        const animatedElements = document.querySelectorAll(
            '.concept-card, .example-item, .strategy-item, .article-section'
        );
        
        animatedElements.forEach(el => {
            el.classList.add('animate-on-scroll');
            observer.observe(el);
        });

        // Add CSS for animations
        this.addAnimationStyles();
    }

    addAnimationStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .animate-on-scroll {
                opacity: 0;
                transform: translateY(30px);
                transition: opacity 0.6s ease-out, transform 0.6s ease-out;
            }
            
            .animate-on-scroll.animate-in {
                opacity: 1;
                transform: translateY(0);
            }
            
            .concept-card.animate-on-scroll {
                transition-delay: 0.1s;
            }
            
            .concept-card:nth-child(2).animate-on-scroll {
                transition-delay: 0.2s;
            }
            
            .concept-card:nth-child(3).animate-on-scroll {
                transition-delay: 0.3s;
            }
            
            .concept-card:nth-child(4).animate-on-scroll {
                transition-delay: 0.4s;
            }
        `;
        document.head.appendChild(style);
    }

    // Keyboard Navigation
    setupKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            // Toggle theme with 'T' key
            if (e.key.toLowerCase() === 't' && !e.ctrlKey && !e.metaKey) {
                const themeToggle = document.getElementById('themeToggle');
                if (document.activeElement.tagName !== 'INPUT' && 
                    document.activeElement.tagName !== 'TEXTAREA') {
                    themeToggle.click();
                }
            }
            
            // Scroll to top with 'Home' key
            if (e.key === 'Home' && e.ctrlKey) {
                e.preventDefault();
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }
            
            // Scroll to bottom with 'End' key
            if (e.key === 'End' && e.ctrlKey) {
                e.preventDefault();
                window.scrollTo({ 
                    top: document.documentElement.scrollHeight, 
                    behavior: 'smooth' 
                });
            }
        });
    }

    // Local Storage for User Preferences
    saveUserPreference(key, value) {
        try {
            localStorage.setItem(`blog_${key}`, value);
        } catch (e) {
            console.warn('Could not save user preference:', e);
        }
    }

    getUserPreference(key) {
        try {
            return localStorage.getItem(`blog_${key}`);
        } catch (e) {
            console.warn('Could not load user preference:', e);
            return null;
        }
    }

    loadUserPreferences() {
        // Load theme preference
        const savedTheme = this.getUserPreference('theme');
        if (savedTheme) {
            document.body.setAttribute('data-theme', savedTheme);
            this.updateThemeIcon(savedTheme);
        } else {
            // Default to system preference
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            const defaultTheme = prefersDark ? 'dark' : 'light';
            document.body.setAttribute('data-theme', defaultTheme);
            this.updateThemeIcon(defaultTheme);
        }
    }

    // Reading Time Estimation
    calculateReadingTime() {
        const article = document.querySelector('.article');
        const text = article.textContent || article.innerText;
        const wordsPerMinute = 200;
        const words = text.trim().split(/\s+/).length;
        const readingTime = Math.ceil(words / wordsPerMinute);
        
        const readingTimeElement = document.querySelector('.reading-time');
        if (readingTimeElement) {
            readingTimeElement.innerHTML = `
                <i class="fas fa-clock"></i>
                ${readingTime} min read
            `;
        }
    }

    // Copy to Clipboard Functionality
    setupCopyToClipboard() {
        const copyButtons = document.querySelectorAll('.copy-btn');
        
        copyButtons.forEach(button => {
            button.addEventListener('click', async () => {
                const textToCopy = button.getAttribute('data-copy');
                
                try {
                    await navigator.clipboard.writeText(textToCopy);
                    this.showToast('Copied to clipboard!');
                } catch (err) {
                    console.error('Failed to copy text: ', err);
                    this.showToast('Failed to copy text', 'error');
                }
            });
        });
    }

    // Toast Notification System
    showToast(message, type = 'success') {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        
        // Add toast styles
        toast.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: ${type === 'success' ? '#10b981' : '#ef4444'};
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease-in-out;
        `;
        
        document.body.appendChild(toast);
        
        // Animate in
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 100);
        
        // Remove after 3 seconds
        setTimeout(() => {
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 3000);
    }

    // Print Functionality
    setupPrintButton() {
        const printButton = document.querySelector('.print-btn');
        if (printButton) {
            printButton.addEventListener('click', () => {
                window.print();
            });
        }
    }

    // Share Functionality
    setupShareButton() {
        const shareButton = document.querySelector('.share-btn');
        if (shareButton) {
            shareButton.addEventListener('click', async () => {
                if (navigator.share) {
                    try {
                        await navigator.share({
                            title: document.title,
                            text: document.querySelector('meta[name="description"]').content,
                            url: window.location.href
                        });
                    } catch (err) {
                        console.log('Error sharing:', err);
                    }
                } else {
                    // Fallback to copying URL
                    try {
                        await navigator.clipboard.writeText(window.location.href);
                        this.showToast('URL copied to clipboard!');
                    } catch (err) {
                        console.error('Failed to copy URL:', err);
                    }
                }
            });
        }
    }
}

// Performance Monitoring
class PerformanceMonitor {
    constructor() {
        this.measurePageLoad();
    }

    measurePageLoad() {
        window.addEventListener('load', () => {
            const perfData = performance.getEntriesByType('navigation')[0];
            const loadTime = perfData.loadEventEnd - perfData.loadEventStart;
            
            console.log(`Page loaded in ${loadTime}ms`);
            
            // You could send this data to analytics
            if (loadTime > 3000) {
                console.warn('Page load time is slower than expected');
            }
        });
    }
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const blog = new BlogInteractivity();
    const monitor = new PerformanceMonitor();
    
    // Calculate reading time
    blog.calculateReadingTime();
    
    // Add some additional interactive features
    blog.setupCopyToClipboard();
    blog.setupPrintButton();
    blog.setupShareButton();
    
    console.log('Blog interactivity initialized successfully!');
});

// Handle page visibility changes
document.addEventListener('visibilitychange', () => {
    if (document.hidden) {
        console.log('User switched away from the page');
    } else {
        console.log('User returned to the page');
    }
});

// Service Worker Registration (for future PWA features)
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        // Uncomment when you have a service worker
        // navigator.serviceWorker.register('/sw.js')
        //     .then(registration => console.log('SW registered'))
        //     .catch(error => console.log('SW registration failed'));
    });
}
